# Professional Full-Stack Developer Portfolio

A modern, responsive portfolio website built with Next.js 15, TypeScript, and Tailwind CSS. Features smooth animations, dark mode support, interactive components, and optimized performance.

## 🚀 Features

### Core Functionality
- **Responsive Design**: Fully responsive layout that works seamlessly across desktop, tablet, and mobile devices
- **Dark Mode Support**: Toggle between light and dark themes with smooth transitions
- **Smooth Animations**: Powered by Framer Motion for engaging user interactions
- **Interactive Components**: Dynamic skill filtering, animated progress bars, and hover effects
- **Contact Form**: Functional contact form with server-side validation and submission handling
- **SEO Optimized**: Comprehensive meta tags, Open Graph, Twitter Cards, and JSON-LD structured data

### Technical Features
- **Next.js 15**: Latest version with App Router and server components
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework for rapid styling
- **Framer Motion**: Advanced animations and micro-interactions
- **Performance Optimized**: Image optimization, font optimization, and bundle optimization
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js**: Version 18.0 or higher
- **npm**: Version 8.0 or higher (comes with Node.js)
- **Git**: For version control

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd portfolio-website
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

## 📁 Project Structure

```
portfolio-website/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout with metadata
│   │   └── page.tsx            # Main page component
│   ├── components/             # React components
│   │   ├── layout/             # Layout components
│   │   │   ├── Header.tsx      # Navigation header
│   │   │   └── Footer.tsx      # Footer component
│   │   ├── sections/           # Page sections
│   │   │   ├── Hero.tsx        # Hero section with typing animation
│   │   │   ├── About.tsx       # About section
│   │   │   ├── Skills.tsx      # Skills with filtering
│   │   │   ├── Projects.tsx    # Project showcase
│   │   │   ├── Experience.tsx  # Experience timeline
│   │   │   └── Contact.tsx     # Contact form
│   │   └── ui/                 # UI components
│   │       └── Animations.tsx  # Framer Motion components
│   ├── lib/                    # Utility functions
│   │   └── actions.ts          # Server actions
│   └── types/                  # TypeScript type definitions
│       └── index.ts            # Type definitions
├── public/                     # Static assets
├── next.config.ts              # Next.js configuration
├── tailwind.config.js          # Tailwind CSS configuration
├── tsconfig.json              # TypeScript configuration
└── package.json               # Dependencies and scripts
```

## 🎨 Customization Guide

### Personal Information

#### 1. Update Basic Information
Edit the content in each section component:

**Hero Section** (`src/components/sections/Hero.tsx`):
```typescript
// Update name and titles
const name = "Your Name";
const titles = ["Your Title 1", "Your Title 2", "Your Title 3"];
```

**About Section** (`src/components/sections/About.tsx`):
- Update personal description
- Modify journey details
- Change core values and motivations

#### 2. Skills and Technologies
**Skills Section** (`src/components/sections/Skills.tsx`):
```typescript
const skills: Skill[] = [
  {
    name: "Your Skill",
    category: "frontend", // frontend, backend, database, cms, learning, tools
    proficiency: 90, // 0-100
  },
  // Add more skills...
];
```

#### 3. Projects Portfolio
**Projects Section** (`src/components/sections/Projects.tsx`):
```typescript
const projects: Project[] = [
  {
    id: "1",
    title: "Your Project Title",
    description: "Project description",
    technologies: ["Tech1", "Tech2", "Tech3"],
    category: "web", // web, mobile, data, devops
    featured: true,
    githubUrl: "https://github.com/yourusername/project",
    liveUrl: "https://yourproject.com",
    imageUrl: "/path/to/image.jpg"
  },
  // Add more projects...
];
```

#### 4. Professional Experience
**Experience Section** (`src/components/sections/Experience.tsx`):
```typescript
const experiences: Experience[] = [
  {
    id: "1",
    title: "Your Job Title",
    company: "Company Name",
    type: "full-time", // full-time, part-time, contract, freelance, internship
    startDate: "2023-01-01",
    endDate: "2024-01-01", // or null for current position
    description: "Job description",
    technologies: ["Tech1", "Tech2"],
    achievements: [
      "Achievement 1",
      "Achievement 2"
    ]
  },
  // Add more experiences...
];
```

#### 5. Contact Information
**Contact Section** (`src/components/sections/Contact.tsx`):
```typescript
const contactInfo = [
  {
    icon: <EmailIcon />,
    label: "Email",
    value: "<EMAIL>",
    href: "mailto:<EMAIL>"
  },
  // Update other contact methods...
];
```

### Styling and Branding

#### 1. Color Scheme
Update colors in `tailwind.config.js`:
```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#your-color-50',
          500: '#your-color-500',
          900: '#your-color-900',
        }
      }
    }
  }
}
```

#### 2. Typography
Modify fonts in `src/app/layout.tsx`:
```typescript
import { YourFont } from "next/font/google";

const yourFont = YourFont({
  subsets: ["latin"],
  variable: "--font-your-font",
});
```

#### 3. Animations
Customize animations in `src/components/ui/Animations.tsx`:
- Modify animation durations
- Change easing functions
- Add new animation components

### SEO and Metadata

#### 1. Update Metadata
Edit `src/app/layout.tsx`:
```typescript
export const metadata: Metadata = {
  title: "Your Name - Your Title",
  description: "Your professional description",
  keywords: ["your", "keywords", "here"],
  // Update other metadata...
};
```

#### 2. Structured Data
Update the JSON-LD schema:
```typescript
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Person",
  "name": "Your Name",
  "jobTitle": "Your Job Title",
  // Update other schema properties...
};
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect Repository**:
   - Push your code to GitHub
   - Connect your repository to Vercel
   - Deploy automatically

2. **Environment Variables**:
   ```bash
   # Add any required environment variables in Vercel dashboard
   NEXT_PUBLIC_SITE_URL=https://yourdomain.com
   ```

### Netlify

1. **Build Settings**:
   ```bash
   Build command: npm run build
   Publish directory: .next
   ```

2. **Deploy**:
   ```bash
   npm run build
   # Upload .next folder to Netlify
   ```

### Self-Hosted

1. **Build for Production**:
   ```bash
   npm run build
   npm start
   ```

2. **Using PM2**:
   ```bash
   npm install -g pm2
   pm2 start npm --name "portfolio" -- start
   ```

## 📱 Browser Support

- **Chrome**: Latest 2 versions
- **Firefox**: Latest 2 versions
- **Safari**: Latest 2 versions
- **Edge**: Latest 2 versions
- **Mobile**: iOS Safari 12+, Chrome Mobile 80+

## 🔧 Development Scripts

```bash
# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Lint code
npm run lint

# Type checking
npm run type-check
```

## 📊 Performance Optimization

### Built-in Optimizations
- **Image Optimization**: Next.js automatic image optimization
- **Font Optimization**: Google Fonts optimization
- **Bundle Splitting**: Automatic code splitting
- **Compression**: Gzip compression enabled
- **Caching**: Optimized caching headers

### Performance Monitoring
- Use Lighthouse for performance auditing
- Monitor Core Web Vitals
- Analyze bundle size with built-in analyzer

## 🛡️ Security Features

- **Content Security Policy**: Configured headers
- **XSS Protection**: Built-in Next.js protection
- **CSRF Protection**: Form validation and sanitization
- **Secure Headers**: Security headers configured

## 🧪 Testing

### Manual Testing Checklist
- [ ] All sections load correctly
- [ ] Navigation works smoothly
- [ ] Dark mode toggle functions
- [ ] Skills filtering works
- [ ] Contact form submits successfully
- [ ] Responsive design on all devices
- [ ] Animations perform smoothly
- [ ] SEO metadata is correct

### Automated Testing (Optional)
```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom jest

# Run tests
npm test
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or need help with customization:

1. Check the documentation above
2. Review the code comments
3. Create an issue in the repository
4. Contact the developer

## 🙏 Acknowledgments

- **Next.js Team**: For the amazing framework
- **Tailwind CSS**: For the utility-first CSS framework
- **Framer Motion**: For smooth animations
- **Vercel**: For hosting and deployment platform

---

**Built with ❤️ using Next.js, TypeScript, and Tailwind CSS**

