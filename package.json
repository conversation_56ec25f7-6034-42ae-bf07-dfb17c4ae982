{"name": "portfolio-website", "version": "1.0.0", "private": true, "description": "A modern, responsive portfolio website built with Next.js, TypeScript, and Tailwind CSS", "author": "Your Name <<EMAIL>>", "license": "MIT", "keywords": ["portfolio", "nextjs", "typescript", "tailwindcss", "react", "framer-motion"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true npm run build", "export": "next export", "clean": "rm -rf .next out node_modules/.cache", "docker:build": "docker build -t portfolio-website .", "docker:run": "docker run -p 3000:3000 portfolio-website", "docker:compose": "docker-compose up -d", "docker:down": "docker-compose down", "prepare": "husky install || true", "format": "prettier --write .", "format:check": "prettier --check .", "test": "echo \"No tests specified\" && exit 0", "precommit": "npm run lint && npm run type-check", "prebuild": "npm run type-check"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "framer-motion": "^11.15.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/portfolio-website.git"}, "bugs": {"url": "https://github.com/yourusername/portfolio-website/issues"}, "homepage": "https://yourdomain.com"}