export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  technologies: string[];
  category: 'frontend' | 'backend' | 'fullstack' | 'data' | 'devops';
  image: string;
  liveUrl?: string;
  githubUrl?: string;
  features: string[];
  challenges: string[];
}

export interface Skill {
  name: string;
  category: 'frontend' | 'backend' | 'database' | 'cms' | 'learning' | 'tools';
  proficiency: number; // 1-100
  icon?: string;
}

export interface Experience {
  id: string;
  title: string;
  company?: string;
  type: 'project' | 'freelance' | 'personal';
  startDate: string;
  endDate?: string;
  description: string;
  technologies: string[];
  achievements: string[];
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
}

export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

