'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Project } from '@/types';

const Projects = () => {
  const [activeFilter, setActiveFilter] = useState<string>('all');

  const projects: Project[] = [
    {
      id: '1',
      title: 'E-commerce Platform',
      description: 'Full-stack Next.js application with Sanity CMS for content management',
      longDescription: 'A comprehensive e-commerce solution built with Next.js 14, featuring dynamic product catalogs, user authentication, shopping cart functionality, and payment integration. The admin panel is powered by Sanity CMS for easy content management.',
      technologies: ['Next.js', 'TypeScript', 'Sanity CMS', 'Stripe', 'Tailwind CSS'],
      category: 'fullstack',
      image: '/projects/ecommerce.jpg',
      liveUrl: 'https://ecommerce-demo.vercel.app',
      githubUrl: 'https://github.com/developer/ecommerce-platform',
      features: [
        'Dynamic product catalog with filtering and search',
        'User authentication and profile management',
        'Shopping cart and checkout process',
        'Payment integration with <PERSON>e',
        'Admin dashboard with Sanity CMS',
        'Responsive design for all devices'
      ],
      challenges: [
        'Implementing real-time inventory management',
        'Optimizing performance for large product catalogs',
        'Ensuring secure payment processing'
      ]
    },
    {
      id: '2',
      title: 'Task Management Dashboard',
      description: 'React frontend with Express/MongoDB backend for team collaboration',
      longDescription: 'A comprehensive task management application designed for team collaboration. Features include project organization, task assignment, progress tracking, and real-time updates. Built with a React frontend and Express.js backend.',
      technologies: ['React', 'Express.js', 'MongoDB', 'Socket.io', 'Material-UI'],
      category: 'fullstack',
      image: '/projects/taskmanager.jpg',
      liveUrl: 'https://taskmanager-demo.herokuapp.com',
      githubUrl: 'https://github.com/developer/task-manager',
      features: [
        'Project and task organization',
        'Team collaboration tools',
        'Real-time updates with Socket.io',
        'Progress tracking and analytics',
        'File attachments and comments',
        'Role-based access control'
      ],
      challenges: [
        'Implementing real-time synchronization',
        'Designing intuitive user workflows',
        'Scaling for multiple teams'
      ]
    },
    {
      id: '3',
      title: 'Real-time Chat Application',
      description: 'WebSocket implementation with user authentication and message history',
      longDescription: 'A modern chat application featuring real-time messaging, user authentication, chat rooms, and message history. Built with Node.js, Socket.io, and React for seamless real-time communication.',
      technologies: ['React', 'Node.js', 'Socket.io', 'JWT', 'PostgreSQL'],
      category: 'backend',
      image: '/projects/chat.jpg',
      liveUrl: 'https://chat-app-demo.netlify.app',
      githubUrl: 'https://github.com/developer/chat-application',
      features: [
        'Real-time messaging with Socket.io',
        'User authentication and profiles',
        'Multiple chat rooms',
        'Message history and search',
        'File sharing capabilities',
        'Online status indicators'
      ],
      challenges: [
        'Handling concurrent connections',
        'Implementing message encryption',
        'Optimizing database queries for chat history'
      ]
    },
    {
      id: '4',
      title: 'Data Visualization Tool',
      description: 'Python-based dashboard showcasing data engineering skills',
      longDescription: 'An interactive data visualization dashboard built with Python, Pandas, and Plotly. Demonstrates data processing, analysis, and visualization capabilities for business intelligence and reporting.',
      technologies: ['Python', 'Pandas', 'Plotly', 'Streamlit', 'PostgreSQL'],
      category: 'data',
      image: '/projects/dataviz.jpg',
      liveUrl: 'https://dataviz-dashboard.streamlit.app',
      githubUrl: 'https://github.com/developer/data-visualization',
      features: [
        'Interactive charts and graphs',
        'Data filtering and aggregation',
        'Export functionality',
        'Real-time data updates',
        'Custom dashboard layouts',
        'Multi-source data integration'
      ],
      challenges: [
        'Processing large datasets efficiently',
        'Creating responsive visualizations',
        'Implementing real-time data updates'
      ]
    },
    {
      id: '5',
      title: 'DevOps Pipeline Project',
      description: 'CI/CD automation with Docker and cloud deployment',
      longDescription: 'A comprehensive DevOps project demonstrating CI/CD pipeline implementation, containerization with Docker, and cloud deployment strategies. Includes automated testing, deployment, and monitoring.',
      technologies: ['Docker', 'Jenkins', 'AWS', 'Kubernetes', 'Terraform'],
      category: 'devops',
      image: '/projects/devops.jpg',
      githubUrl: 'https://github.com/developer/devops-pipeline',
      features: [
        'Automated CI/CD pipeline',
        'Docker containerization',
        'Kubernetes orchestration',
        'Infrastructure as Code with Terraform',
        'Monitoring and logging',
        'Automated testing integration'
      ],
      challenges: [
        'Designing scalable infrastructure',
        'Implementing security best practices',
        'Optimizing deployment processes'
      ]
    },
    {
      id: '6',
      title: 'API Gateway & Microservices',
      description: 'Scalable backend architecture with microservices pattern',
      longDescription: 'A microservices architecture implementation featuring an API gateway, service discovery, load balancing, and inter-service communication. Demonstrates modern backend development practices.',
      technologies: ['Node.js', 'Express.js', 'Redis', 'Docker', 'NGINX'],
      category: 'backend',
      image: '/projects/microservices.jpg',
      liveUrl: 'https://api-gateway-demo.herokuapp.com',
      githubUrl: 'https://github.com/developer/microservices-api',
      features: [
        'API Gateway with routing',
        'Service discovery and registration',
        'Load balancing and failover',
        'Centralized authentication',
        'Rate limiting and caching',
        'Health monitoring'
      ],
      challenges: [
        'Managing service dependencies',
        'Implementing distributed tracing',
        'Ensuring data consistency'
      ]
    }
  ];

  const filters = [
    { id: 'all', name: 'All Projects', count: projects.length },
    { id: 'fullstack', name: 'Full-Stack', count: projects.filter(p => p.category === 'fullstack').length },
    { id: 'frontend', name: 'Frontend', count: projects.filter(p => p.category === 'frontend').length },
    { id: 'backend', name: 'Backend', count: projects.filter(p => p.category === 'backend').length },
    { id: 'data', name: 'Data Engineering', count: projects.filter(p => p.category === 'data').length },
    { id: 'devops', name: 'DevOps', count: projects.filter(p => p.category === 'devops').length },
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <section id="projects" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Featured Projects
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              A showcase of my work across different technologies and domains
            </p>
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setActiveFilter(filter.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeFilter === filter.id
                    ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 shadow-md'
                }`}
              >
                {filter.name} ({filter.count})
              </button>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid lg:grid-cols-2 gap-8">
            {filteredProjects.map((project, index) => (
              <div
                key={project.id}
                className="bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 overflow-hidden"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Project Image */}
                <div className="relative h-48 bg-gradient-to-br from-blue-400 to-purple-600 overflow-hidden">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold">{project.title}</h3>
                    </div>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {project.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                      {project.description}
                    </p>
                  </div>

                  {/* Technologies */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm font-medium rounded-full"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Key Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Key Features:</h4>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {project.features.slice(0, 3).map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    {project.liveUrl && (
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-center font-medium rounded-lg transition-colors duration-300"
                      >
                        Live Demo
                      </a>
                    )}
                    {project.githubUrl && (
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 px-4 py-2 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-blue-600 hover:text-blue-600 dark:hover:text-blue-400 text-center font-medium rounded-lg transition-colors duration-300"
                      >
                        View Code
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
              Interested in seeing more of my work?
            </p>
            <a
              href="https://github.com/developer"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              View All Projects on GitHub
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;

